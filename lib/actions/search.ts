"use server"

import {
  checkRateLimit,
  logUserAction,
  validateSession
} from "../auth"
import {
  RateLimitError
} from "./shared"
import { debug } from "../debug"
import { supabaseAdmin } from "../supabase"

// Enhanced user context for search access control
interface SearchUserContext {
  id: string
  email: string
  fullName: string
  role: string
  employeeId?: string
  managedEmployeeIds?: string[]
  isManager: boolean
  isEmployee: boolean
}

async function getSearchUserContext(currentUser: any): Promise<SearchUserContext> {
  const context: SearchUserContext = {
    id: currentUser.id,
    email: currentUser.email,
    fullName: currentUser.fullName,
    role: currentUser.role,
    isManager: false,
    isEmployee: false
  }

  console.log('🔍 [SEARCH DEBUG] Building user context for:', currentUser.id)

  // Check if user is a manager
  const { data: managerData } = await supabaseAdmin
    .from('appy_managers')
    .select('user_id')
    .eq('user_id', currentUser.id)
    .eq('active', true)
    .single()

  if (managerData) {
    context.isManager = true
    console.log('👔 [SEARCH DEBUG] User is a manager')

    // Get all employees managed by this user (including hierarchical)
    try {
      const { data: managedEmployees } = await supabaseAdmin
        .from('appy_employees')
        .select('id')
        .eq('manager_id', currentUser.id)
        .eq('active', true)

      context.managedEmployeeIds = managedEmployees?.map(emp => emp.id) || []
      console.log('👥 [SEARCH DEBUG] Manager oversees employees:', context.managedEmployeeIds)
    } catch (error) {
      console.error('❌ [SEARCH ERROR] Failed to get managed employees:', error)
      context.managedEmployeeIds = []
    }
  }

  // Check if user is also an employee (dual role)
  const { data: employeeData } = await supabaseAdmin
    .from('appy_employees')
    .select('id, manager_id')
    .eq('email', currentUser.email)
    .eq('active', true)
    .single()

  if (employeeData) {
    context.isEmployee = true
    context.employeeId = employeeData.id
    console.log('👤 [SEARCH DEBUG] User is also an employee:', employeeData.id)
  }

  console.log('✅ [SEARCH DEBUG] Final user context:', {
    role: context.role,
    isManager: context.isManager,
    isEmployee: context.isEmployee,
    employeeId: context.employeeId,
    managedCount: context.managedEmployeeIds?.length || 0
  })

  return context
}

export async function searchAllEntities(query: string) {
  const searchStartTime = Date.now()
  console.log('🔍 [SEARCH ACTION] === SEARCH ACTION CALLED ===', {
    query,
    queryLength: query.length,
    timestamp: new Date().toISOString()
  })

  try {
    // Authentication check
    const session = await validateSession()
    console.log('🔍 [SEARCH ACTION] Session validated for user:', session.userId)

    // Rate limiting
    if (!checkRateLimit(session.userId, 'search', 30, 60000)) { // 30 per minute
      console.log('🔍 [SEARCH ACTION] Rate limit exceeded for user:', session.userId)
      throw new RateLimitError()
    }

    if (!query.trim()) {
      console.log('🔍 [SEARCH ACTION] Empty query provided, returning empty array')
      return []
    }

    // Import auth function to get current user role
    const { getCurrentUser } = await import('../auth')
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return []
    }

    // Get enhanced user context for access control
    const userContext = await getSearchUserContext(currentUser)

    // Import and use the new FTS search function
    const { searchAllEntitiesFTS } = await import('../search-fts')

    debug.log('🔍 [SEARCH DEBUG] Using FTS search for query:', query)
    debug.log('🔍 [SEARCH DEBUG] Query length:', query.length)
    debug.log('👤 [SEARCH DEBUG] User context:', {
      role: userContext.role,
      isManager: userContext.isManager,
      isEmployee: userContext.isEmployee
    })

    const searchResults = await searchAllEntitiesFTS(query, userContext)

    debug.log('🔍 [SEARCH DEBUG] FTS results with access control:', {
      totalResults: searchResults.length,
      results: searchResults.map(r => ({ type: r.type, title: r.title, score: r.adjustedScore })),
      userContext: {
        role: userContext.role,
        isManager: userContext.isManager,
        isEmployee: userContext.isEmployee,
        managedCount: userContext.managedEmployeeIds?.length || 0
      }
    })

    // Transform FTS results to match expected format (remove relevance scores)
    const finalResults = searchResults.map(result => ({
      id: result.id,
      title: result.title,
      subtitle: result.subtitle,
      href: result.href,
      type: result.type,
      icon: result.icon
    }))

    // Log search action with access control info (for analytics, rate limiting)
    await logUserAction('search:query', {
      query: query.trim(),
      resultCount: finalResults.length,
      userId: session.userId,
      userRole: userContext.role,
      isManager: userContext.isManager,
      isEmployee: userContext.isEmployee,
      accessControlApplied: true
    })

    const searchEndTime = Date.now()
    const totalSearchTime = searchEndTime - searchStartTime

    debug.log('🔍 [SEARCH DEBUG] Final results with access control applied:', {
      count: finalResults.length,
      types: finalResults.reduce((acc, r) => {
        acc[r.type] = (acc[r.type] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      totalSearchTime: `${totalSearchTime}ms`
    })

    console.log('🔍 [SEARCH ACTION] === SEARCH ACTION COMPLETE ===', {
      query,
      resultCount: finalResults.length,
      totalTime: `${totalSearchTime}ms`,
      success: true
    })

    return finalResults

  } catch (error) {
    console.error('❌ [SEARCH ERROR] FTS search failed, falling back to empty results:', error)
    return []
  }
}