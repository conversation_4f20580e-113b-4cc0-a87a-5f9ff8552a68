/**
 * Full-Text Search Implementation using Postgres FTS
 * 
 * This module implements server-side Full-Text Search using Supabase's 
 * built-in Postgres FTS capabilities for superior performance and accuracy.
 */

import { supabaseAdmin } from './supabase'
import { debug } from './debug'

// Search result type
export interface SearchResult {
  id: string
  title: string
  subtitle?: string
  href: string
  type: "employee" | "department" | "manager" | "period" | "navigation" | "pto"
  icon: string
  relevanceScore: number
  adjustedScore: number
}

// Type-specific search result interfaces
interface EmployeeSearchResult {
  id: string
  full_name: string
  department_name?: string
  manager_name?: string
  compensation: number
  rate: string
  rank: number
}

interface DepartmentSearchResult {
  id: string
  name: string
  rank: number
}

interface ManagerSearchResult {
  user_id: string
  full_name: string
  rank: number
}

interface PeriodSearchResult {
  id: string
  name: string
  start_date: string
  end_date: string
  status: string
  rank: number
}

interface PTOSearchResult {
  id: string
  employee_name: string
  request_type: string
  start_date: string
  days_requested: number
  status: string
  rank: number
}

/**
 * Sanitizes search input to prevent injection and improve FTS parsing
 */
function sanitizeSearchQuery(query: string): string {
  console.log('🔧 [SANITIZE DEBUG] Input query:', { query, length: query.length })

  if (!query || typeof query !== 'string') {
    console.log('🔧 [SANITIZE DEBUG] Invalid query type, returning empty')
    return ''
  }

  const sanitized = query
    .trim()
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, ' ') // Remove special chars, keep alphanumeric and spaces
    .replace(/\s+/g, ' ') // Normalize multiple spaces
    .substring(0, 100) // Limit query length
    .trim() // Final trim

  // Allow single character searches for better UX
  console.log('🔧 [SANITIZE DEBUG] Sanitized query:', {
    original: query,
    sanitized,
    length: sanitized.length,
    allowShortSearch: sanitized.length >= 1 // Changed from potential 2-char minimum
  })

  return sanitized
}

/**
 * Converts search term to tsquery format for Postgres FTS
 */
function createTSQuery(sanitizedTerm: string): string {
  // Split into words and join with & for AND logic
  const words = sanitizedTerm.split(' ').filter(word => word.length > 0)
  if (words.length === 0) return ''
  
  // Create prefix search for partial word matching
  const tsquery = words.map(word => `${word}:*`).join(' & ')
  console.log('🔧 [TSQUERY DEBUG] Created tsquery:', { sanitizedTerm, words, tsquery })
  return tsquery
}

/**
 * Search employees using Postgres Full-Text Search with access control
 */
async function searchEmployees(searchTerm: string, userContext: SearchUserContext, limit: number = 5): Promise<SearchResult[]> {
  const sanitizedTerm = sanitizeSearchQuery(searchTerm)
  if (!sanitizedTerm || sanitizedTerm.length < 1) {
    console.log('🔍 [FTS DEBUG] Employee search: empty or too short sanitized term:', {
      original: searchTerm,
      sanitized: sanitizedTerm,
      length: sanitizedTerm.length
    })
    return []
  }

  console.log('🔍 [FTS DEBUG] Employee search with access control:', {
    searchTerm,
    sanitizedTerm,
    limit,
    userRole: userContext.role,
    isManager: userContext.isManager,
    managedEmployees: userContext.managedEmployeeIds?.length || 0
  })

  try {
    console.log('🔍 [FTS DEBUG] About to execute textSearch with access control:', {
      sanitizedTerm,
      limit: limit * 2
    })

    // Build base query
    let query = supabaseAdmin
      .from('appy_employees')
      .select(`
        id,
        full_name,
        compensation,
        rate,
        email,
        appy_departments:department_id (
          name
        )
      `)
      .eq('active', true)

    // Apply access control filters based on user role and context
    if (userContext.role === 'super-admin' || userContext.role === 'hr-admin' || userContext.role === 'admin') {
      // Admins can see all employees - no additional filtering
      console.log('🔓 [ACCESS DEBUG] Admin access: showing all employees')
    } else if (userContext.isManager && userContext.managedEmployeeIds && userContext.managedEmployeeIds.length > 0) {
      // Managers can only see their direct reports
      query = query.in('id', userContext.managedEmployeeIds)
      console.log('👔 [ACCESS DEBUG] Manager access: filtering to managed employees:', userContext.managedEmployeeIds)
    } else if (userContext.isEmployee && userContext.employeeId) {
      // Regular employees can only see themselves
      query = query.eq('id', userContext.employeeId)
      console.log('👤 [ACCESS DEBUG] Employee access: showing only self:', userContext.employeeId)
    } else {
      // No access - return empty results
      console.log('🚫 [ACCESS DEBUG] No employee access for user')
      return []
    }

    // Try textSearch first, but with better error handling
    let data, error

    try {
      const result = await query
        .textSearch('full_name', sanitizedTerm, {
          type: 'websearch',
          config: 'english'
        })
        .limit(limit * 2) // Get more to allow for better ranking

      data = result.data
      error = result.error

      console.log('🔍 [FTS DEBUG] textSearch completed with access control:', {
        hasData: !!data,
        dataLength: data?.length || 0,
        hasError: !!error,
        errorMessage: error?.message || 'none'
      })
    } catch (textSearchError) {
      console.error('❌ [FTS ERROR] textSearch threw exception:', textSearchError)
      error = textSearchError
    }

    if (error) {
      console.error('❌ [FTS ERROR] Employee textSearch failed, trying fallback:', error)
      console.error('❌ [FTS ERROR] Full error object:', JSON.stringify(error, null, 2))
      // Don't throw, fall through to fallback
    }

    // If textSearch failed or returned no data, use fallback
    if (error || !data || data.length === 0) {
      console.log('🔍 [FTS DEBUG] Using ILIKE fallback for employees with access control')

      // Rebuild the same access-controlled query for fallback
      let fallbackQuery = supabaseAdmin
        .from('appy_employees')
        .select(`
          id,
          full_name,
          compensation,
          rate,
          email,
          appy_departments:department_id (
            name
          )
        `)
        .eq('active', true)

      // Apply the same access control filters
      if (userContext.role === 'super-admin' || userContext.role === 'hr-admin' || userContext.role === 'admin') {
        // Admins can see all employees - no additional filtering
      } else if (userContext.isManager && userContext.managedEmployeeIds && userContext.managedEmployeeIds.length > 0) {
        // Managers can only see their direct reports
        fallbackQuery = fallbackQuery.in('id', userContext.managedEmployeeIds)
      } else if (userContext.isEmployee && userContext.employeeId) {
        // Regular employees can only see themselves
        fallbackQuery = fallbackQuery.eq('id', userContext.employeeId)
      } else {
        // No access - return empty results
        return []
      }

      const { data: fallbackData, error: fallbackError } = await fallbackQuery
        .ilike('full_name', `%${sanitizedTerm}%`)
        .limit(limit)

      if (fallbackError) {
        console.error('❌ [FTS ERROR] Fallback search failed:', fallbackError)
        return []
      }

      console.log('🔍 [FTS DEBUG] Fallback found employees:', fallbackData?.length || 0)

      return (fallbackData || []).map((emp, index) => ({
        id: `employee-${emp.id}`,
        title: emp.full_name,
        subtitle: `${emp.appy_departments?.[0]?.name || 'No Department'} • ${emp.rate === 'hourly' ? 'Hourly' : 'Monthly'}`,
        href: `/dashboard/appraisal/${emp.id}`,
        type: 'employee' as const,
        icon: 'user',
        relevanceScore: 50 - (index * 5), // Decreasing score for fallback results
        adjustedScore: 70 - (index * 5) // Boost for employee type
      }))
    }

    console.log('✅ [FTS DEBUG] Found employees with access control:', data.map(emp => emp.full_name))

    // Check if user can see sensitive compensation data
    const canSeeSensitiveData = ['super-admin', 'hr-admin', 'admin'].includes(userContext.role)

    return data.map((emp, index) => {
      // Build subtitle based on access level
      let subtitle = emp.appy_departments?.[0]?.name || 'No Department'

      if (canSeeSensitiveData) {
        // Admins can see compensation info
        subtitle += ` • ${emp.rate === 'hourly' ? 'Hourly' : 'Monthly'}`
      } else {
        // Non-admins see department only
        subtitle += ` • Employee`
      }

      return {
        id: `employee-${emp.id}`,
        title: emp.full_name,
        subtitle,
        href: `/dashboard/appraisal/${emp.id}`,
        type: 'employee' as const,
        icon: 'user',
        relevanceScore: 100 - (index * 10), // Higher scores for better FTS matches
        adjustedScore: 120 - (index * 10) // Boost for employee type
      }
    })

  } catch (error) {
    console.error('❌ [FTS ERROR] Employee search error:', error)
    return []
  }
}

/**
 * Search departments using Postgres Full-Text Search
 */
async function searchDepartments(searchTerm: string, limit: number = 3): Promise<SearchResult[]> {
  const sanitizedTerm = sanitizeSearchQuery(searchTerm)
  if (!sanitizedTerm) return []

  console.log('🔍 [FTS DEBUG] Department search:', { searchTerm, sanitizedTerm })

  try {
    let data, error

    try {
      const result = await supabaseAdmin
        .from('appy_departments')
        .select('id, name')
        .textSearch('name', sanitizedTerm, {
          type: 'websearch',
          config: 'english'
        })
        .limit(limit)

      data = result.data
      error = result.error
    } catch (textSearchError) {
      console.error('❌ [FTS ERROR] Department textSearch threw exception:', textSearchError)
      error = textSearchError
    }

    if (error || !data || data.length === 0) {
      console.log('🔍 [FTS DEBUG] Using ILIKE fallback for departments')
      // Fallback to ILIKE
      const { data: fallbackData, error: fallbackError } = await supabaseAdmin
        .from('appy_departments')
        .select('id, name')
        .ilike('name', `%${sanitizedTerm}%`)
        .limit(limit)

      if (fallbackError) {
        console.error('❌ [FTS ERROR] Department fallback failed:', fallbackError)
        return []
      }

      return (fallbackData || []).map((dept, index) => ({
        id: `department-${dept.id}`,
        title: dept.name,
        subtitle: 'Department',
        href: '/dashboard/departments',
        type: 'department' as const,
        icon: 'building2',
        relevanceScore: 40 - (index * 5),
        adjustedScore: 45 - (index * 5) // Lower boost for departments
      }))
    }

    return (data || []).map((dept, index) => ({
      id: `department-${dept.id}`,
      title: dept.name,
      subtitle: 'Department',
      href: '/dashboard/departments',
      type: 'department' as const,
      icon: 'building2',
      relevanceScore: 80 - (index * 10),
      adjustedScore: 85 - (index * 10) // Lower boost for departments
    }))

  } catch (error) {
    console.error('❌ [FTS ERROR] Department search error:', error)
    return []
  }
}

/**
 * Search managers using Postgres Full-Text Search
 */
async function searchManagers(searchTerm: string, userContext: SearchUserContext, limit: number = 3): Promise<SearchResult[]> {
  const sanitizedTerm = sanitizeSearchQuery(searchTerm)
  if (!sanitizedTerm) return []

  console.log('🔍 [FTS DEBUG] Manager search with access control:', {
    searchTerm,
    sanitizedTerm,
    userRole: userContext.role,
    isManager: userContext.isManager
  })

  // Only admins and managers can search for managers
  if (!['super-admin', 'hr-admin', 'admin', 'manager'].includes(userContext.role)) {
    console.log('🚫 [ACCESS DEBUG] User role cannot search managers:', userContext.role)
    return []
  }

  try {
    let data, error

    try {
      let query = supabaseAdmin
        .from('appy_managers')
        .select('user_id, full_name, email')
        .eq('active', true)

      // Apply access control - for now, managers can see all other managers
      // In a more complex hierarchy, you might want to limit this further
      if (userContext.role === 'manager' && !['super-admin', 'hr-admin', 'admin'].includes(userContext.role)) {
        console.log('👔 [ACCESS DEBUG] Manager can see all managers (could be restricted further)')
        // Could add: .neq('user_id', userContext.id) to exclude self
      }

      const result = await query
        .textSearch('full_name', sanitizedTerm, {
          type: 'websearch',
          config: 'english'
        })
        .limit(limit)

      data = result.data
      error = result.error
    } catch (textSearchError) {
      console.error('❌ [FTS ERROR] Manager textSearch threw exception:', textSearchError)
      error = textSearchError
    }

    if (error || !data || data.length === 0) {
      console.log('🔍 [FTS DEBUG] Using ILIKE fallback for managers with access control')

      let fallbackQuery = supabaseAdmin
        .from('appy_managers')
        .select('user_id, full_name, email')
        .eq('active', true)

      // Apply same access control for fallback
      if (userContext.role === 'manager' && !['super-admin', 'hr-admin', 'admin'].includes(userContext.role)) {
        // Could add restrictions here
      }

      const { data: fallbackData, error: fallbackError } = await fallbackQuery
        .ilike('full_name', `%${sanitizedTerm}%`)
        .limit(limit)

      if (fallbackError) {
        console.error('❌ [FTS ERROR] Manager fallback failed:', fallbackError)
        return []
      }

      return (fallbackData || []).map((mgr, index) => ({
        id: `manager-${mgr.user_id}`,
        title: mgr.full_name,
        subtitle: 'Manager',
        href: '/dashboard/team',
        type: 'manager' as const,
        icon: 'user-check',
        relevanceScore: 35 - (index * 5),
        adjustedScore: 45 - (index * 5) // Medium boost for managers
      }))
    }

    return (data || []).map((mgr, index) => ({
      id: `manager-${mgr.user_id}`,
      title: mgr.full_name,
      subtitle: 'Manager',
      href: '/dashboard/team',
      type: 'manager' as const,
      icon: 'user-check',
      relevanceScore: 70 - (index * 10),
      adjustedScore: 80 - (index * 10) // Medium boost for managers
    }))

  } catch (error) {
    console.error('❌ [FTS ERROR] Manager search error:', error)
    return []
  }
}

/**
 * Search appraisal periods using Postgres Full-Text Search
 */
async function searchPeriods(searchTerm: string, limit: number = 2): Promise<SearchResult[]> {
  const sanitizedTerm = sanitizeSearchQuery(searchTerm)
  if (!sanitizedTerm) return []

  console.log('🔍 [FTS DEBUG] Period search:', { searchTerm, sanitizedTerm })

  try {
    let data, error

    try {
      const result = await supabaseAdmin
        .from('appy_appraisal_periods')
        .select('id, name, start_date, end_date, status')
        .textSearch('name', sanitizedTerm, {
          type: 'websearch',
          config: 'english'
        })
        .limit(limit)

      data = result.data
      error = result.error
    } catch (textSearchError) {
      console.error('❌ [FTS ERROR] Period textSearch threw exception:', textSearchError)
      error = textSearchError
    }

    if (error || !data || data.length === 0) {
      console.log('🔍 [FTS DEBUG] Using ILIKE fallback for periods')
      // Fallback to ILIKE
      const { data: fallbackData, error: fallbackError } = await supabaseAdmin
        .from('appy_appraisal_periods')
        .select('id, name, start_date, end_date, status')
        .ilike('name', `%${sanitizedTerm}%`)
        .limit(limit)

      if (fallbackError) {
        console.error('❌ [FTS ERROR] Period fallback failed:', fallbackError)
        return []
      }

      return (fallbackData || []).map((period, index) => ({
        id: `period-${period.id}`,
        title: period.name,
        subtitle: `${period.status} • ${new Date(period.start_date).getFullYear()}`,
        href: '/dashboard/periods',
        type: 'period' as const,
        icon: 'calendar',
        relevanceScore: 30 - (index * 5),
        adjustedScore: 35 - (index * 5)
      }))
    }

    return (data || []).map((period, index) => ({
      id: `period-${period.id}`,
      title: period.name,
      subtitle: `${period.status} • ${new Date(period.start_date).getFullYear()}`,
      href: '/dashboard/periods',
      type: 'period' as const,
      icon: 'calendar',
      relevanceScore: 60 - (index * 10),
      adjustedScore: 65 - (index * 10)
    }))

  } catch (error) {
    console.error('❌ [FTS ERROR] Period search error:', error)
    return []
  }
}

/**
 * Search PTO requests using Postgres Full-Text Search with access control
 */
async function searchPTORequests(searchTerm: string, userContext: SearchUserContext, limit: number = 2): Promise<SearchResult[]> {
  const sanitizedTerm = sanitizeSearchQuery(searchTerm)
  if (!sanitizedTerm) return []

  console.log('🔍 [FTS DEBUG] PTO search with access control:', {
    searchTerm,
    sanitizedTerm,
    userRole: userContext.role,
    isManager: userContext.isManager,
    isEmployee: userContext.isEmployee
  })

  try {
    let query = supabaseAdmin
      .from('appy_pto_requests')
      .select(`
        id,
        employee_id,
        request_type,
        start_date,
        end_date,
        status,
        reason,
        appy_employees!inner (
          id,
          full_name,
          appy_departments (
            name
          )
        )
      `)

    // Apply access control filters
    if (userContext.role === 'super-admin' || userContext.role === 'hr-admin' || userContext.role === 'admin') {
      // Admins can see all PTO requests - no additional filtering
      console.log('🔓 [ACCESS DEBUG] Admin access: showing all PTO requests')
    } else if (userContext.isManager && userContext.managedEmployeeIds && userContext.managedEmployeeIds.length > 0) {
      // Managers can see PTO requests from their direct reports
      query = query.in('employee_id', userContext.managedEmployeeIds)
      console.log('👔 [ACCESS DEBUG] Manager access: filtering to managed employees PTO:', userContext.managedEmployeeIds)
    } else if (userContext.isEmployee && userContext.employeeId) {
      // Regular employees can only see their own PTO requests
      query = query.eq('employee_id', userContext.employeeId)
      console.log('👤 [ACCESS DEBUG] Employee access: showing only own PTO requests:', userContext.employeeId)
    } else {
      // No access - return empty results
      console.log('🚫 [ACCESS DEBUG] No PTO access for user')
      return []
    }

    // Search by request type using simple ILIKE (avoid complex OR with joins for now)
    let data, error

    try {
      // Use simple ILIKE search on request_type field
      const result = await query
        .ilike('request_type', `%${sanitizedTerm}%`)
        .order('created_at', { ascending: false })
        .limit(limit)

      data = result.data
      error = result.error

      console.log('🔍 [FTS DEBUG] PTO search completed:', {
        hasData: !!data,
        dataLength: data?.length || 0,
        hasError: !!error,
        errorMessage: error?.message || 'none'
      })
    } catch (searchError) {
      console.error('❌ [FTS ERROR] PTO search threw exception:', searchError)
      error = searchError
    }

    if (error || !data) {
      console.error('❌ [FTS ERROR] PTO search failed:', error)
      return []
    }

    return data.map((pto, index) => {
      // Handle the nested employee data structure
      const employee = Array.isArray(pto.appy_employees) ? pto.appy_employees[0] : pto.appy_employees
      const employeeName = employee?.full_name || 'Unknown Employee'

      return {
        id: `pto-${pto.id}`,
        title: `${pto.request_type.charAt(0).toUpperCase() + pto.request_type.slice(1)} Request`,
        subtitle: `${employeeName} • ${new Date(pto.start_date).toLocaleDateString()} - ${new Date(pto.end_date).toLocaleDateString()} • ${pto.status}`,
        href: `/dashboard/pto/${pto.id}`,
        type: 'pto' as const,
        icon: 'calendar-days',
        relevanceScore: 50 - (index * 10),
        adjustedScore: 55 - (index * 10)
      }
    })

  } catch (error) {
    console.error('❌ [FTS ERROR] PTO search error:', error)
    return []
  }
}

/**
 * Search navigation pages (static content)
 */
function searchNavigation(searchTerm: string): SearchResult[] {
  const navigationItems = [
    { id: 'nav-dashboard', title: 'Dashboard', href: '/dashboard', icon: 'navigation' },
    { id: 'nav-employees', title: 'Employees', href: '/dashboard/employees', icon: 'navigation' },
    { id: 'nav-departments', title: 'Departments', href: '/dashboard/departments', icon: 'navigation' },
    { id: 'nav-periods', title: 'Appraisal Periods', href: '/dashboard/periods', icon: 'navigation' },
    { id: 'nav-approvals', title: 'Approvals', href: '/dashboard/approvals', icon: 'navigation' },
    { id: 'nav-pto', title: 'PTO Management', href: '/dashboard/pto', icon: 'navigation' },
    { id: 'nav-team', title: 'Team View', href: '/dashboard/team', icon: 'navigation' },
    { id: 'nav-admin', title: 'Admin Panel', href: '/dashboard/admin', icon: 'navigation' }
  ]

  const term = searchTerm.toLowerCase()
  
  return navigationItems
    .filter(item => item.title.toLowerCase().includes(term))
    .map((item, index) => ({
      id: item.id,
      title: item.title,
      subtitle: 'Navigation',
      href: item.href,
      type: 'navigation' as const,
      icon: item.icon,
      relevanceScore: 40 - (index * 5),
      adjustedScore: 40 - (index * 5) // No boost for navigation
    }))
    .slice(0, 2)
}

/**
 * Deduplicate results, handling dual-role entities (e.g., employee who is also manager)
 */
function deduplicateResults(results: SearchResult[]): SearchResult[] {
  const seen = new Map<string, SearchResult>()
  
  return results.filter(result => {
    // Extract base ID (remove type prefix)
    const baseId = result.id.replace(/^(employee|manager|department|period|navigation|pto)-/, '')
    const key = `${result.type}-${baseId}`
    
    if (seen.has(key)) return false
    
    // Handle dual-role entities (employee who is also manager)
    if (result.type === 'manager') {
      const employeeKey = `employee-${baseId}`
      if (seen.has(employeeKey)) {
        // Merge roles: show as "Employee (Manager)"
        const existingEmployee = seen.get(employeeKey)!
        existingEmployee.title += ' (Manager)'
        existingEmployee.adjustedScore += 5 // Small boost for dual role
        return false
      }
    }
    
    if (result.type === 'employee') {
      const managerKey = `manager-${baseId}`
      if (seen.has(managerKey)) {
        // Update to show dual role
        result.title += ' (Manager)'
        result.adjustedScore += 5 // Small boost for dual role
      }
    }
    
    seen.set(key, result)
    return true
  })
}

// Enhanced user context for search access control
interface SearchUserContext {
  id: string
  email: string
  fullName: string
  role: string
  employeeId?: string
  managedEmployeeIds?: string[]
  isManager: boolean
  isEmployee: boolean
}

/**
 * Main FTS search function that combines all entity searches
 */
export async function searchAllEntitiesFTS(query: string, userContext: SearchUserContext): Promise<SearchResult[]> {
  const startTime = Date.now()

  console.log('🔍 [FTS DEBUG] === SEARCH START ===', {
    originalQuery: query,
    queryLength: query.length,
    userRole: userContext.role,
    isManager: userContext.isManager,
    isEmployee: userContext.isEmployee
  })

  if (!query.trim()) {
    console.log('🔍 [FTS DEBUG] Empty query provided')
    return []
  }

  const sanitizedQuery = sanitizeSearchQuery(query)
  console.log('🔍 [FTS DEBUG] After sanitization:', {
    original: query,
    sanitized: sanitizedQuery,
    sanitizedLength: sanitizedQuery.length
  })

  if (!sanitizedQuery) {
    console.log('🔍 [FTS DEBUG] Query sanitization resulted in empty string')
    return []
  }

  console.log('🔍 [FTS DEBUG] Starting search with user context:', {
    query,
    sanitizedQuery,
    userRole: userContext.role,
    managedEmployees: userContext.managedEmployeeIds?.length || 0
  })

  try {
    // Define role-based access control
    const hasEmployeeAccess = ['hr-admin', 'admin', 'super-admin', 'manager'].includes(userContext.role)
    const hasAdminAccess = ['hr-admin', 'admin', 'super-admin'].includes(userContext.role)

    // Run searches in parallel for better performance
    const searchPromises: Promise<SearchResult[]>[] = []

    if (hasEmployeeAccess) {
      searchPromises.push(searchEmployees(sanitizedQuery, userContext, 6)) // More employees as they're primary
    }

    if (hasAdminAccess) {
      searchPromises.push(searchDepartments(sanitizedQuery, 3))
      searchPromises.push(searchManagers(sanitizedQuery, userContext, 4))
      searchPromises.push(searchPeriods(sanitizedQuery, 2))
    }

    // PTO and Navigation available to all authenticated users
    searchPromises.push(searchPTORequests(sanitizedQuery, userContext, 2))
    searchPromises.push(Promise.resolve(searchNavigation(sanitizedQuery)))

    const searchResults = await Promise.all(searchPromises)
    
    // Flatten and combine all results
    const allResults = searchResults.flat()
    
    console.log('🔍 [FTS DEBUG] Raw results before deduplication:', allResults.length)
    
    // Deduplicate and sort by adjusted score
    const deduplicatedResults = deduplicateResults(allResults)
    const sortedResults = deduplicatedResults
      .sort((a, b) => b.adjustedScore - a.adjustedScore)
      .slice(0, 10) // Limit to top 10 results
    
    const endTime = Date.now()
    
    // Log comprehensive access control summary
    console.log('🔍 [FTS DEBUG] === SEARCH COMPLETE WITH ACCESS CONTROL ===', {
      query: sanitizedQuery,
      userRole: userContext.role,
      accessControlSummary: {
        isManager: userContext.isManager,
        isEmployee: userContext.isEmployee,
        managedEmployeeCount: userContext.managedEmployeeIds?.length || 0,
        employeeId: userContext.employeeId || 'none',
        canSeeAllEmployees: ['super-admin', 'hr-admin', 'admin'].includes(userContext.role),
        canSeeManagers: ['super-admin', 'hr-admin', 'admin', 'manager'].includes(userContext.role),
        canSeePTO: true, // All authenticated users can see PTO (with filtering)
        canSeeDepartments: ['super-admin', 'hr-admin', 'admin'].includes(userContext.role),
        canSeePeriods: ['super-admin', 'hr-admin', 'admin'].includes(userContext.role)
      },
      totalResults: sortedResults.length,
      searchTime: `${endTime - startTime}ms`,
      resultTypes: sortedResults.reduce((acc, r) => {
        acc[r.type] = (acc[r.type] || 0) + 1
        return acc
      }, {} as Record<string, number>),
      resultTitles: sortedResults.map(r => `${r.type}: ${r.title}`)
    })
    
    return sortedResults

  } catch (error) {
    debug.error('❌ [FTS ERROR] Search failed:', error)
    debug.error('❌ [FTS ERROR] Error details:', {
      message: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : 'No stack trace',
      query: sanitizedQuery,
      userRole: userContext.role,
      userContext: {
        isManager: userContext.isManager,
        isEmployee: userContext.isEmployee,
        managedCount: userContext.managedEmployeeIds?.length || 0
      }
    })
    return []
  }
}

/**
 * Initialize FTS indexes (run this once in your database)
 * This would typically be run as a migration
 */
export async function initializeFTSIndexes(): Promise<void> {
  console.log('🔧 [FTS SETUP] Initializing Full-Text Search indexes...')
  
  const indexes = [
    // Employee full-text search index
    `CREATE INDEX IF NOT EXISTS idx_employees_fts 
     ON appy_employees USING GIN (to_tsvector('english', full_name))`,
    
    // Department full-text search index
    `CREATE INDEX IF NOT EXISTS idx_departments_fts 
     ON appy_departments USING GIN (to_tsvector('english', name))`,
    
    // Manager full-text search index
    `CREATE INDEX IF NOT EXISTS idx_managers_fts 
     ON appy_managers USING GIN (to_tsvector('english', full_name))`,
    
    // Appraisal period full-text search index
    `CREATE INDEX IF NOT EXISTS idx_periods_fts 
     ON appy_appraisal_periods USING GIN (to_tsvector('english', name))`,
     
    // PTO request type full-text search index
    `CREATE INDEX IF NOT EXISTS idx_pto_fts 
     ON appy_pto_requests USING GIN (to_tsvector('english', request_type))`
  ]

  try {
    for (const indexSQL of indexes) {
      await supabaseAdmin.rpc('exec_sql', { sql: indexSQL })
      console.log('✅ [FTS SETUP] Index created successfully')
    }
    
    console.log('🚀 [FTS SETUP] All FTS indexes initialized successfully!')
  } catch (error) {
    console.error('❌ [FTS SETUP] Failed to initialize indexes:', error)
    throw error
  }
}