# Global Search - Complete File Listing

This document contains ALL files related to the global search functionality in the Employee Appraisal Management System.

## 📁 File Structure Overview

```
├── components/
│   ├── global-search.tsx           # Main search UI component
│   ├── app-header.tsx              # Search button trigger
│   └── ui/
│       ├── command.tsx             # Command palette components
│       └── badge.tsx               # Badge component for result types
├── hooks/
│   └── use-global-search.ts        # Search state management hook
├── lib/
│   ├── actions/
│   │   ├── search.ts               # Search server action
│   │   └── index.ts                # Action exports
│   ├── search-fts.ts               # Full-Text Search implementation
│   ├── actions.ts                  # Legacy action references
│   ├── debug.ts                    # Debug utilities
│   ├── auth.ts                     # Authentication functions
│   └── supabase.ts                 # Supabase client
├── app/
│   └── dashboard/
│       └── layout.tsx              # Dashboard layout with search
├── scripts/
│   └── setup-fts-indexes.ts       # FTS index setup script
├── docs/
│   ├── global-search-implementation.md  # Architecture documentation
│   └── global-search-complete-files.md  # This file
├── CLAUDE.md                       # System documentation
└── package.json                    # Dependencies
```

## 🔍 Core Search Components

### 1. Main Search UI Component
**File: `components/global-search.tsx`**

This is the primary search interface component that renders the command palette.

**Key Features:**
- Command palette interface using shadcn/ui
- Grouped results by entity type
- Icon mapping and visual badges
- Keyboard navigation support
- Recent searches display
- Loading states and error handling

**Dependencies:**
- `@/components/ui/command` - Command palette components
- `@/components/ui/badge` - Result type badges
- `@/hooks/use-global-search` - Search state management
- `lucide-react` - Icons

### 2. Search State Management Hook
**File: `hooks/use-global-search.ts`**

Manages all search-related state and behavior.

**Key Features:**
- Debounced search with 300ms delay
- Keyboard shortcut handling (Cmd/Ctrl+K, Escape)
- Recent searches persistence with localStorage
- Result state management
- Navigation handling

**Dependencies:**
- `lodash` - For debouncing
- `next/navigation` - For routing
- `@/lib/actions` - Search API calls

### 3. Search Server Action
**File: `lib/actions/search.ts`**

Server-side search implementation with authentication and rate limiting.

**Key Features:**
- Authentication validation
- Rate limiting (30 requests per minute)
- Role-based access control
- FTS search integration
- Comprehensive logging
- Error handling

### 4. Full-Text Search Implementation
**File: `lib/search-fts.ts`**

Advanced Postgres Full-Text Search implementation.

**Key Features:**
- Postgres FTS with websearch configuration
- ILIKE fallback for compatibility
- Multi-entity search (employees, departments, managers, periods, PTO, navigation)
- Relevance scoring and result ranking
- Role-based filtering
- Deduplication handling
- Performance optimization

## 🎨 UI Components

### 5. Command Palette Components
**File: `components/ui/command.tsx`**

shadcn/ui command palette implementation.

**Components:**
- `CommandDialog` - Modal wrapper
- `CommandInput` - Search input field
- `CommandList` - Results container
- `CommandGroup` - Result grouping
- `CommandItem` - Individual result
- `CommandSeparator` - Visual separators

### 6. Badge Component
**File: `components/ui/badge.tsx`**

Used for displaying result type labels with color coding.

### 7. App Header Integration
**File: `components/app-header.tsx`**

Contains the search trigger button with keyboard shortcut display.

**Features:**
- Search button with Cmd+K shortcut hint
- Integration with global search hook
- Responsive design (icon-only on mobile)

## 🏗️ Layout Integration

### 8. Dashboard Layout
**File: `app/dashboard/layout.tsx`**

Includes the GlobalSearch component in the main dashboard layout.

**Integration:**
- GlobalSearch component rendered at layout level
- Available across all dashboard pages
- Proper accessibility with skip links

## 📚 Documentation

### 9. Architecture Documentation
**File: `docs/global-search-implementation.md`**

Comprehensive documentation covering:
- Current architecture analysis
- Performance considerations
- Best practices research
- Migration roadmap
- Troubleshooting guides

### 10. System Documentation
**File: `CLAUDE.md`**

Contains search system overview in the broader system documentation.

## 🛠️ Setup & Configuration

### 11. FTS Index Setup Script
**File: `scripts/setup-fts-indexes.ts`**

Script to initialize Postgres Full-Text Search indexes.

**Indexes Created:**
- `idx_employees_fts` - Employee names
- `idx_departments_fts` - Department names  
- `idx_managers_fts` - Manager names
- `idx_periods_fts` - Appraisal period names
- `idx_pto_fts` - PTO request types

### 12. Action Exports
**File: `lib/actions/index.ts`**

Re-exports the `searchAllEntities` function for backward compatibility.

## 🔧 Supporting Infrastructure

### 13. Debug Utilities
**File: `lib/debug.ts`**

Provides conditional logging functionality used throughout the search system.

### 14. Authentication Functions
**File: `lib/auth.ts`**

Contains authentication functions used by search:
- `validateSession()` - Session validation
- `getCurrentUser()` - User role retrieval
- `checkRateLimit()` - Rate limiting
- `logUserAction()` - Action logging

### 15. Supabase Client
**File: `lib/supabase.ts`**

Supabase client configuration used by FTS search implementation.

### 16. Legacy Actions File
**File: `lib/actions.ts`**

Contains references to search functionality and migration notes.

## 📦 Dependencies

### 17. Package Configuration
**File: `package.json`**

Key dependencies for search functionality:
- `lodash` - Debouncing
- `cmdk` - Command palette
- `@radix-ui/react-dialog` - Modal components
- `lucide-react` - Icons

## 🚀 Search Features Summary

### ✅ Implemented Features
- **Command Palette UI** with keyboard navigation
- **Keyboard Shortcuts** (Cmd/Ctrl+K to open, Escape to close)  
- **Debounced Search** (300ms delay)
- **Recent Searches** with localStorage persistence
- **Full-Text Search** with Postgres FTS and ILIKE fallback
- **Role-based Access Control** for search results
- **Multi-entity Search** (employees, departments, managers, periods, navigation, PTO)
- **Result Grouping** by entity type with icons and badges
- **Comprehensive Logging** for debugging
- **Rate Limiting** and authentication
- **FTS Indexes** for performance optimization
- **Responsive Design** with mobile support
- **Accessibility** features
- **Error Handling** and fallback mechanisms

### 🎯 Entity Types Searched
1. **Employees** - Names, departments, compensation info
2. **Departments** - Department names
3. **Managers** - Manager names and details
4. **Appraisal Periods** - Period names and dates
5. **Navigation Pages** - Dashboard pages and routes
6. **PTO Requests** - Request types and details

### 🔐 Security Features
- **Authentication** validation on all search requests
- **Rate Limiting** (30 requests per minute)
- **Role-based Access Control** for different user types
- **Input Sanitization** for search queries
- **Session Validation** before processing requests

### 📊 Performance Optimizations
- **Postgres FTS Indexes** for fast text search
- **Debounced API Calls** to reduce server load
- **Result Caching** in component state
- **Parallel Search Execution** across entity types
- **Relevance Scoring** for better result ranking
- **Result Deduplication** for dual-role entities

This search system provides a comprehensive, performant, and secure global search experience across the entire Employee Appraisal Management System.

---

## 📄 Complete File Contents

### 1. `components/global-search.tsx`

```tsx
"use client"

import React from "react"
import {
  CommandDialog,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "@/components/ui/command"
import { Badge } from "@/components/ui/badge"
import { useGlobalSearch } from "@/hooks/use-global-search"
import {
  User,
  Building2,
  UserCheck,
  Calendar,
  Navigation,
  CalendarDays,
  Search,
  Clock,
  Loader2,
} from "lucide-react"

const iconMap = {
  user: User,
  building2: Building2,
  "user-check": UserCheck,
  calendar: Calendar,
  navigation: Navigation,
  "calendar-days": CalendarDays,
}

const typeLabels = {
  employee: "Employee",
  department: "Department",
  manager: "Manager",
  period: "Appraisal Period",
  navigation: "Page",
  pto: "PTO Request",
}

const typeColors = {
  employee: "bg-blue-100 text-blue-800",
  department: "bg-green-100 text-green-800",
  manager: "bg-purple-100 text-purple-800",
  period: "bg-orange-100 text-orange-800",
  navigation: "bg-gray-100 text-gray-800",
  pto: "bg-pink-100 text-pink-800",
}

export function GlobalSearch() {
  const {
    isOpen,
    setIsOpen,
    query,
    setQuery,
    results,
    loading,
    selectResult,
    showingRecent,
  } = useGlobalSearch()

  // Group results by type for better organization
  const groupedResults = React.useMemo(() => {
    if (!results || results.length === 0) return {}

    return results.reduce((acc, result) => {
      if (!result || !result.type) {
        console.warn('🔍 [SEARCH UI] Invalid result structure:', result)
        return acc
      }

      if (!acc[result.type]) {
        acc[result.type] = []
      }
      acc[result.type].push(result)
      return acc
    }, {} as Record<string, typeof results>)
  }, [results])

  console.log('🔍 [SEARCH UI] Render state:', {
    isOpen,
    query,
    resultsLength: results?.length || 0,
    loading,
    showingRecent,
    groupedResultsKeys: Object.keys(groupedResults)
  })

  const handleSelect = (result: any) => {
    try {
      if (!result) {
        console.warn('🔍 [SEARCH UI] Attempted to select null/undefined result')
        return
      }
      selectResult(result)
    } catch (error) {
      console.error('🔍 [SEARCH UI] Error selecting result:', error)
    }
  }

  return (
    <CommandDialog open={isOpen} onOpenChange={setIsOpen}>
      <CommandInput
        placeholder="Search employees, departments, pages..."
        value={query}
        onValueChange={setQuery}
        className="flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50"
      />
      {loading && (
        <div className="flex items-center justify-center px-3 py-2">
          <Loader2 className="h-4 w-4 animate-spin opacity-50" />
        </div>
      )}
      <CommandList>
        <CommandEmpty>
          {query.trim() ? "No results found." : "Start typing to search..."}
        </CommandEmpty>

        {showingRecent && results.length > 0 && (
          <CommandGroup heading="Recent">
            {results.map((result) => {
              const IconComponent = iconMap[result.icon as keyof typeof iconMap] || User
              return (
                <CommandItem
                  key={result.id}
                  value={result.id}
                  onSelect={() => handleSelect(result)}
                  className="flex items-center gap-3 px-3 py-2"
                >
                  <div className="flex h-8 w-8 items-center justify-center rounded-md bg-muted">
                    <IconComponent className="h-4 w-4" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="font-medium truncate">{result.title}</span>
                      <Badge
                        variant="secondary"
                        className={`text-xs ${typeColors[result.type]}`}
                      >
                        {typeLabels[result.type]}
                      </Badge>
                    </div>
                    {result.subtitle && (
                      <p className="text-sm text-muted-foreground truncate">
                        {result.subtitle}
                      </p>
                    )}
                  </div>
                  <Clock className="h-3 w-3 text-muted-foreground" />
                </CommandItem>
              )
            })}
          </CommandGroup>
        )}

        {!showingRecent && (
          <>
            {Object.entries(groupedResults).map(([type, typeResults], index) => (
              <div key={type}>
                {index > 0 && <CommandSeparator />}
                <CommandGroup heading={typeLabels[type as keyof typeof typeLabels] || type}>
                  {typeResults.map((result) => {
                    if (!result || !result.id) {
                      console.warn('🔍 [SEARCH UI] Skipping invalid result:', result)
                      return null
                    }

                    const IconComponent = iconMap[result.icon as keyof typeof iconMap] || User
                    return (
                      <CommandItem
                        key={result.id}
                        value={result.id}
                        onSelect={() => handleSelect(result)}
                        className="flex items-center gap-3 px-3 py-2"
                      >
                        <div className="flex h-8 w-8 items-center justify-center rounded-md bg-muted">
                          <IconComponent className="h-4 w-4" />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2">
                            <span className="font-medium truncate">{result.title || 'Untitled'}</span>
                            <Badge
                              variant="secondary"
                              className={`text-xs ${typeColors[result.type]}`}
                            >
                              {typeLabels[result.type]}
                            </Badge>
                          </div>
                          {result.subtitle && (
                            <p className="text-sm text-muted-foreground truncate">
                              {result.subtitle}
                            </p>
                          )}
                        </div>
                      </CommandItem>
                    )
                  })}
                </CommandGroup>
              </div>
            ))}
          </>
        )}

        {!showingRecent && results.length > 0 && (
          <>
            <CommandSeparator />
            <div className="px-3 py-2 text-xs text-muted-foreground">
              <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
                <span className="text-xs">⏎</span>
              </kbd>{" "}
              to select •{" "}
              <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
                <span className="text-xs">↑↓</span>
              </kbd>{" "}
              to navigate •{" "}
              <kbd className="pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
                <span className="text-xs">ESC</span>
              </kbd>{" "}
              to close
            </div>
          </>
        )}
      </CommandList>
    </CommandDialog>
  )
}
```

### 2. `hooks/use-global-search.ts`

```tsx
"use client"

import { useState, useEffect, useCallback, useMemo } from "react"
import { useRouter } from "next/navigation"
import { searchAllEntities } from "@/lib/actions"
import { debounce } from "lodash"

export type SearchResult = {
  id: string
  title: string
  subtitle?: string
  href: string
  type: "employee" | "department" | "manager" | "period" | "navigation" | "pto"
  icon: string
}

export function useGlobalSearch() {
  const [isOpen, setIsOpen] = useState(false)
  const [query, setQuery] = useState("")
  const [results, setResults] = useState<SearchResult[]>([])
  const [loading, setLoading] = useState(false)
  const [recentSearches, setRecentSearches] = useState<SearchResult[]>([])
  const router = useRouter()

  // Perform search with error handling
  const performSearch = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      console.log('🔍 [FRONTEND DEBUG] Empty search query, skipping API call')
      setResults(recentSearches.slice(0, 5))
      setLoading(false)
      return
    }

    console.log('🔍 [FRONTEND DEBUG] Starting search for:', searchQuery)
    setLoading(true)

    try {
      const searchResults = await searchAllEntities(searchQuery.trim())
      console.log('🔍 [FRONTEND DEBUG] Search results received:', {
        query: searchQuery,
        resultCount: searchResults?.length || 0,
        results: searchResults
      })

      if (Array.isArray(searchResults)) {
        setResults(searchResults)
      } else {
        console.warn('🔍 [FRONTEND DEBUG] Invalid search results format:', searchResults)
        setResults([])
      }
    } catch (error) {
      console.error('🔍 [FRONTEND ERROR] Search failed:', error)
      setResults([])
    } finally {
      setLoading(false)
    }
  }, [recentSearches])

  // Create debounced search function
  const debouncedSearch = useMemo(
    () => debounce(performSearch, 300),
    [performSearch]
  )

  // Handle search query changes
  useEffect(() => {
    if (!query.trim()) {
      console.log('🔍 [FRONTEND DEBUG] Empty query, showing recent searches')
      setResults(recentSearches.slice(0, 5))
      setLoading(false)
      debouncedSearch.cancel() // Cancel any pending searches
      return
    }

    debouncedSearch(query)
    return () => debouncedSearch.cancel()
  }, [query, debouncedSearch, recentSearches])

  // Load recent searches from localStorage on mount
  useEffect(() => {
    try {
      const stored = localStorage.getItem("global-search-recent")
      if (stored) {
        setRecentSearches(JSON.parse(stored))
      }
    } catch (error) {
      console.error("Failed to load recent searches:", error)
    }
  }, [])

  // Save recent searches to localStorage
  const saveRecentSearch = useCallback((result: SearchResult) => {
    try {
      setRecentSearches(prev => {
        // Remove if already exists, then add to front
        const filtered = prev.filter(item => item.id !== result.id)
        const updated = [result, ...filtered].slice(0, 10) // Keep only 10 recent
        localStorage.setItem("global-search-recent", JSON.stringify(updated))
        return updated
      })
    } catch (error) {
      console.error("Failed to save recent search:", error)
    }
  }, [])

  // Handle result selection
  const selectResult = useCallback((result: SearchResult) => {
    console.log('🔍 [FRONTEND DEBUG] Selecting result:', result)

    if (!result?.href) {
      console.warn('🔍 [FRONTEND DEBUG] Result missing href:', result)
      return
    }

    saveRecentSearch(result)
    setIsOpen(false)
    setQuery("")
    router.push(result.href)
  }, [router, saveRecentSearch])

  // Keyboard shortcut handler
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "k" && (event.metaKey || event.ctrlKey)) {
        event.preventDefault()
        setIsOpen(prev => !prev)
      }

      if (event.key === "Escape" && isOpen) {
        event.preventDefault()
        setIsOpen(false)
        setQuery("")
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    return () => document.removeEventListener("keydown", handleKeyDown)
  }, [isOpen])

  // Reset when closing
  useEffect(() => {
    if (!isOpen) {
      setQuery("")
      setResults(recentSearches.slice(0, 5))
    }
  }, [isOpen, recentSearches])

  // 🔧 FIX: Simplify results logic to prevent display issues
  const displayResults = query.trim() ? results : recentSearches.slice(0, 5)

  console.log('🔍 [FRONTEND DEBUG] Display logic:', {
    hasQuery: !!query.trim(),
    queryLength: query.length,
    resultsLength: results.length,
    recentLength: recentSearches.length,
    displayLength: displayResults.length,
    loading
  })

  return {
    isOpen,
    setIsOpen,
    query,
    setQuery,
    results: displayResults,
    loading,
    selectResult,
    showingRecent: !query.trim(),
  }
}
```

### 3. `lib/actions/search.ts`

```tsx
"use server"

import {
  checkRateLimit,
  logUserAction,
  validateSession
} from "../auth"
import {
  handleServerActionError,
  RateLimitError
} from "./shared"
import { debug } from "../debug"

export async function searchAllEntities(query: string) {
  try {
    // Authentication check
    const session = await validateSession()

    // Rate limiting
    if (!checkRateLimit(session.userId, 'search', 30, 60000)) { // 30 per minute
      throw new RateLimitError()
    }

    if (!query.trim()) {
      return []
    }

    // Import auth function to get current user role
    const { getCurrentUser } = await import('../auth')
    const currentUser = await getCurrentUser()
    if (!currentUser) {
      return []
    }

    // Import and use the new FTS search function
    const { searchAllEntitiesFTS } = await import('../search-fts')

    debug.log('🔍 [SEARCH DEBUG] Using FTS search for query:', query)
    debug.log('🔍 [SEARCH DEBUG] Query length:', query.length)
    debug.log('👤 [SEARCH DEBUG] User role:', currentUser.role)

    const searchResults = await searchAllEntitiesFTS(query, currentUser.role)

    debug.log('🔍 [SEARCH DEBUG] FTS results:', {
      totalResults: searchResults.length,
      results: searchResults.map(r => ({ type: r.type, title: r.title, score: r.adjustedScore }))
    })

    // Transform FTS results to match expected format (remove relevance scores)
    const finalResults = searchResults.map(result => ({
      id: result.id,
      title: result.title,
      subtitle: result.subtitle,
      href: result.href,
      type: result.type,
      icon: result.icon
    }))

    // Log search action (for analytics, rate limiting)
    await logUserAction('search:query', {
      query: query.trim(),
      resultCount: finalResults.length,
      userId: session.userId,
    })

    debug.log('🔍 [SEARCH DEBUG] Final results being returned:', finalResults)
    return finalResults

  } catch (error) {
    console.error('❌ [SEARCH ERROR] FTS search failed, falling back to empty results:', error)
    return []
  }
}
```

### 4. `lib/search-fts.ts` (Part 1 - Types and Core Functions)

```tsx
/**
 * Full-Text Search Implementation using Postgres FTS
 *
 * This module implements server-side Full-Text Search using Supabase's
 * built-in Postgres FTS capabilities for superior performance and accuracy.
 */

import { supabaseAdmin } from './supabase'
import { debug } from './debug'

// Search result type
export interface SearchResult {
  id: string
  title: string
  subtitle?: string
  href: string
  type: "employee" | "department" | "manager" | "period" | "navigation" | "pto"
  icon: string
  relevanceScore: number
  adjustedScore: number
}

// Type-specific search result interfaces
interface EmployeeSearchResult {
  id: string
  full_name: string
  department_name?: string
  manager_name?: string
  compensation: number
  rate: string
  rank: number
}

interface DepartmentSearchResult {
  id: string
  name: string
  rank: number
}

interface ManagerSearchResult {
  id: string
  full_name: string
  rank: number
}

interface PeriodSearchResult {
  id: string
  name: string
  start_date: string
  end_date: string
  rank: number
}

interface PTOSearchResult {
  id: string
  request_type: string
  start_date: string
  end_date: string
  status: string
  rank: number
}

/**
 * Sanitize search query to prevent injection and improve FTS compatibility
 */
function sanitizeSearchQuery(query: string): string {
  if (!query || typeof query !== 'string') return ''

  // Remove special characters that could break FTS, but keep spaces and basic punctuation
  const sanitized = query
    .trim()
    .replace(/[^\w\s\-\.]/g, ' ') // Keep alphanumeric, spaces, hyphens, dots
    .replace(/\s+/g, ' ') // Normalize multiple spaces
    .trim()

  console.log('🔍 [FTS DEBUG] Query sanitization:', { original: query, sanitized })
  return sanitized
}

/**
 * Search employees using Postgres Full-Text Search
 */
async function searchEmployees(searchTerm: string, limit: number = 5): Promise<SearchResult[]> {
  const sanitizedTerm = sanitizeSearchQuery(searchTerm)
  if (!sanitizedTerm) return []

  console.log('🔍 [FTS DEBUG] Employee search:', { searchTerm, sanitizedTerm, limit })

  // Try textSearch first, but with better error handling
  let data, error

  try {
    const result = await supabaseAdmin
      .from('appy_employees')
      .select(`
        id,
        full_name,
        compensation,
        rate,
        appy_departments:department_id (
          name
        )
      `)
      .eq('active', true)
      .textSearch('full_name', sanitizedTerm, {
        type: 'websearch',
        config: 'english'
      })
      .limit(limit * 2) // Get more to allow for better ranking

    data = result.data
    error = result.error

    console.log('🔍 [FTS DEBUG] textSearch completed:', {
      hasData: !!data,
      dataLength: data?.length || 0,
      hasError: !!error,
      errorMessage: error?.message || 'none'
    })
  } catch (textSearchError) {
    console.error('❌ [FTS ERROR] textSearch threw exception:', textSearchError)
    error = textSearchError
  }

  // If textSearch failed or returned no data, use fallback
  if (error || !data || data.length === 0) {
    console.log('🔍 [FTS DEBUG] Using ILIKE fallback for employees')

    // Fallback to ILIKE for partial matches
    const { data: fallbackData, error: fallbackError } = await supabaseAdmin
      .from('appy_employees')
      .select(`
        id,
        full_name,
        compensation,
        rate,
        appy_departments:department_id (
          name
        )
      `)
      .eq('active', true)
      .ilike('full_name', `%${sanitizedTerm}%`)
      .limit(limit)

    if (fallbackError) {
      console.error('❌ [FTS ERROR] Fallback search failed:', fallbackError)
      return []
    }

    data = fallbackData
  }

  if (!data || data.length === 0) {
    console.log('🔍 [FTS DEBUG] No employee data found')
    return []
  }

  console.log('🔍 [FTS DEBUG] Employee data found:', data.length, 'records')

  return data.map((employee: any, index: number) => ({
    id: employee.id,
    title: employee.full_name,
    subtitle: employee.appy_departments?.name
      ? `${employee.appy_departments.name} • $${employee.compensation?.toLocaleString() || 'N/A'} ${employee.rate || ''}`
      : `$${employee.compensation?.toLocaleString() || 'N/A'} ${employee.rate || ''}`,
    href: `/dashboard/employees?id=${employee.id}`,
    type: 'employee' as const,
    icon: 'user',
    relevanceScore: 50 - (index * 5), // Higher score for earlier results
    adjustedScore: 50 - (index * 5) // No boost for employees
  })).slice(0, limit)
}
```

### 5. `lib/search-fts.ts` (Part 2 - Department and Manager Search)

```tsx
/**
 * Search departments using Postgres Full-Text Search
 */
async function searchDepartments(searchTerm: string, limit: number = 3): Promise<SearchResult[]> {
  const sanitizedTerm = sanitizeSearchQuery(searchTerm)
  if (!sanitizedTerm) return []

  console.log('🔍 [FTS DEBUG] Department search:', { searchTerm, sanitizedTerm })

  try {
    let data, error

    try {
      const result = await supabaseAdmin
        .from('appy_departments')
        .select('id, name')
        .textSearch('name', sanitizedTerm, {
          type: 'websearch',
          config: 'english'
        })
        .limit(limit)

      data = result.data
      error = result.error
    } catch (textSearchError) {
      console.error('❌ [FTS ERROR] Department textSearch threw exception:', textSearchError)
      error = textSearchError
    }

    // Fallback to ILIKE if textSearch fails
    if (error || !data || data.length === 0) {
      console.log('🔍 [FTS DEBUG] Using ILIKE fallback for departments')

      const { data: fallbackData, error: fallbackError } = await supabaseAdmin
        .from('appy_departments')
        .select('id, name')
        .ilike('name', `%${sanitizedTerm}%`)
        .limit(limit)

      if (fallbackError) {
        console.error('❌ [FTS ERROR] Department fallback failed:', fallbackError)
        return []
      }

      data = fallbackData
    }

    if (!data || data.length === 0) {
      console.log('🔍 [FTS DEBUG] No department data found')
      return []
    }

    console.log('🔍 [FTS DEBUG] Department data found:', data.length, 'records')

    return data.map((dept: any, index: number) => ({
      id: dept.id,
      title: dept.name,
      subtitle: 'Department',
      href: `/dashboard/departments?id=${dept.id}`,
      type: 'department' as const,
      icon: 'building2',
      relevanceScore: 45 - (index * 5),
      adjustedScore: 45 - (index * 5) // No boost for departments
    }))

  } catch (error) {
    console.error('❌ [FTS ERROR] Department search failed:', error)
    return []
  }
}

/**
 * Search managers using Postgres Full-Text Search
 */
async function searchManagers(searchTerm: string, limit: number = 4): Promise<SearchResult[]> {
  const sanitizedTerm = sanitizeSearchQuery(searchTerm)
  if (!sanitizedTerm) return []

  console.log('🔍 [FTS DEBUG] Manager search:', { searchTerm, sanitizedTerm })

  try {
    let data, error

    try {
      const result = await supabaseAdmin
        .from('appy_managers')
        .select('id, full_name')
        .textSearch('full_name', sanitizedTerm, {
          type: 'websearch',
          config: 'english'
        })
        .limit(limit)

      data = result.data
      error = result.error
    } catch (textSearchError) {
      console.error('❌ [FTS ERROR] Manager textSearch threw exception:', textSearchError)
      error = textSearchError
    }

    // Fallback to ILIKE if textSearch fails
    if (error || !data || data.length === 0) {
      console.log('🔍 [FTS DEBUG] Using ILIKE fallback for managers')

      const { data: fallbackData, error: fallbackError } = await supabaseAdmin
        .from('appy_managers')
        .select('id, full_name')
        .ilike('full_name', `%${sanitizedTerm}%`)
        .limit(limit)

      if (fallbackError) {
        console.error('❌ [FTS ERROR] Manager fallback failed:', fallbackError)
        return []
      }

      data = fallbackData
    }

    if (!data || data.length === 0) {
      console.log('🔍 [FTS DEBUG] No manager data found')
      return []
    }

    console.log('🔍 [FTS DEBUG] Manager data found:', data.length, 'records')

    return data.map((manager: any, index: number) => ({
      id: manager.id,
      title: manager.full_name,
      subtitle: 'Manager',
      href: `/dashboard/employees?manager=${manager.id}`,
      type: 'manager' as const,
      icon: 'user-check',
      relevanceScore: 40 - (index * 5),
      adjustedScore: 40 - (index * 5) // No boost for managers
    }))

  } catch (error) {
    console.error('❌ [FTS ERROR] Manager search failed:', error)
    return []
  }
}
```

### 6. `lib/search-fts.ts` (Part 3 - Period, PTO, and Navigation Search)

```tsx
/**
 * Search appraisal periods using Postgres Full-Text Search
 */
async function searchPeriods(searchTerm: string, limit: number = 2): Promise<SearchResult[]> {
  const sanitizedTerm = sanitizeSearchQuery(searchTerm)
  if (!sanitizedTerm) return []

  console.log('🔍 [FTS DEBUG] Period search:', { searchTerm, sanitizedTerm })

  try {
    let data, error

    try {
      const result = await supabaseAdmin
        .from('appy_appraisal_periods')
        .select('id, name, start_date, end_date')
        .textSearch('name', sanitizedTerm, {
          type: 'websearch',
          config: 'english'
        })
        .limit(limit)

      data = result.data
      error = result.error
    } catch (textSearchError) {
      console.error('❌ [FTS ERROR] Period textSearch threw exception:', textSearchError)
      error = textSearchError
    }

    // Fallback to ILIKE if textSearch fails
    if (error || !data || data.length === 0) {
      console.log('🔍 [FTS DEBUG] Using ILIKE fallback for periods')

      const { data: fallbackData, error: fallbackError } = await supabaseAdmin
        .from('appy_appraisal_periods')
        .select('id, name, start_date, end_date')
        .ilike('name', `%${sanitizedTerm}%`)
        .limit(limit)

      if (fallbackError) {
        console.error('❌ [FTS ERROR] Period fallback failed:', fallbackError)
        return []
      }

      data = fallbackData
    }

    if (!data || data.length === 0) {
      console.log('🔍 [FTS DEBUG] No period data found')
      return []
    }

    console.log('🔍 [FTS DEBUG] Period data found:', data.length, 'records')

    return data.map((period: any, index: number) => {
      const startDate = new Date(period.start_date).toLocaleDateString()
      const endDate = new Date(period.end_date).toLocaleDateString()

      return {
        id: period.id,
        title: period.name,
        subtitle: `${startDate} - ${endDate}`,
        href: `/dashboard/periods?id=${period.id}`,
        type: 'period' as const,
        icon: 'calendar',
        relevanceScore: 35 - (index * 5),
        adjustedScore: 35 - (index * 5) // No boost for periods
      }
    })

  } catch (error) {
    console.error('❌ [FTS ERROR] Period search failed:', error)
    return []
  }
}

/**
 * Search PTO requests using Postgres Full-Text Search
 */
async function searchPTO(searchTerm: string, limit: number = 2): Promise<SearchResult[]> {
  const sanitizedTerm = sanitizeSearchQuery(searchTerm)
  if (!sanitizedTerm) return []

  console.log('🔍 [FTS DEBUG] PTO search:', { searchTerm, sanitizedTerm })

  try {
    let data, error

    try {
      const result = await supabaseAdmin
        .from('appy_pto_requests')
        .select('id, request_type, start_date, end_date, status')
        .textSearch('request_type', sanitizedTerm, {
          type: 'websearch',
          config: 'english'
        })
        .limit(limit)

      data = result.data
      error = result.error
    } catch (textSearchError) {
      console.error('❌ [FTS ERROR] PTO textSearch threw exception:', textSearchError)
      error = textSearchError
    }

    // Fallback to ILIKE if textSearch fails
    if (error || !data || data.length === 0) {
      console.log('🔍 [FTS DEBUG] Using ILIKE fallback for PTO')

      const { data: fallbackData, error: fallbackError } = await supabaseAdmin
        .from('appy_pto_requests')
        .select('id, request_type, start_date, end_date, status')
        .ilike('request_type', `%${sanitizedTerm}%`)
        .limit(limit)

      if (fallbackError) {
        console.error('❌ [FTS ERROR] PTO fallback failed:', fallbackError)
        return []
      }

      data = fallbackData
    }

    if (!data || data.length === 0) {
      console.log('🔍 [FTS DEBUG] No PTO data found')
      return []
    }

    console.log('🔍 [FTS DEBUG] PTO data found:', data.length, 'records')

    return data.map((pto: any, index: number) => {
      const startDate = new Date(pto.start_date).toLocaleDateString()
      const endDate = new Date(pto.end_date).toLocaleDateString()

      return {
        id: pto.id,
        title: `${pto.request_type} Request`,
        subtitle: `${startDate} - ${endDate} • ${pto.status}`,
        href: `/dashboard/pto?id=${pto.id}`,
        type: 'pto' as const,
        icon: 'calendar-days',
        relevanceScore: 30 - (index * 5),
        adjustedScore: 30 - (index * 5) // No boost for PTO
      }
    })

  } catch (error) {
    console.error('❌ [FTS ERROR] PTO search failed:', error)
    return []
  }
}

/**
 * Search navigation pages (static content)
 */
function searchNavigation(searchTerm: string): SearchResult[] {
  const navigationItems = [
    { id: 'nav-dashboard', title: 'Dashboard', href: '/dashboard', icon: 'navigation' },
    { id: 'nav-employees', title: 'Employees', href: '/dashboard/employees', icon: 'navigation' },
    { id: 'nav-departments', title: 'Departments', href: '/dashboard/departments', icon: 'navigation' },
    { id: 'nav-periods', title: 'Appraisal Periods', href: '/dashboard/periods', icon: 'navigation' },
    { id: 'nav-approvals', title: 'Approvals', href: '/dashboard/approvals', icon: 'navigation' },
    { id: 'nav-pto', title: 'PTO Management', href: '/dashboard/pto', icon: 'navigation' },
    { id: 'nav-team', title: 'Team View', href: '/dashboard/team', icon: 'navigation' },
    { id: 'nav-admin', title: 'Admin Panel', href: '/dashboard/admin', icon: 'navigation' }
  ]

  const term = searchTerm.toLowerCase()

  return navigationItems
    .filter(item => item.title.toLowerCase().includes(term))
    .map((item, index) => ({
      id: item.id,
      title: item.title,
      subtitle: 'Navigation',
      href: item.href,
      type: 'navigation' as const,
      icon: item.icon,
      relevanceScore: 40 - (index * 5),
      adjustedScore: 40 - (index * 5) // No boost for navigation
    }))
    .slice(0, 2)
}
```

### 7. `lib/search-fts.ts` (Part 4 - Main Search Function and Utilities)

```tsx
/**
 * Deduplicate results, handling dual-role entities (e.g., employee who is also manager)
 */
function deduplicateResults(results: SearchResult[]): SearchResult[] {
  const seen = new Map<string, SearchResult>()

  for (const result of results) {
    const key = `${result.title}-${result.type}`

    if (!seen.has(key) || (seen.get(key)?.adjustedScore || 0) < result.adjustedScore) {
      seen.set(key, result)
    }
  }

  return Array.from(seen.values())
}

/**
 * Main FTS search function that orchestrates all entity searches
 */
export async function searchAllEntitiesFTS(query: string, userRole: string): Promise<SearchResult[]> {
  const sanitizedQuery = sanitizeSearchQuery(query)
  if (!sanitizedQuery) {
    console.log('🔍 [FTS DEBUG] Empty or invalid query after sanitization')
    return []
  }

  console.log('🔍 [FTS DEBUG] Starting search:', { query, sanitizedQuery, userRole })

  try {
    // Define role-based access control
    const hasEmployeeAccess = ['hr-admin', 'admin', 'super-admin', 'manager'].includes(userRole)
    const hasAdminAccess = ['hr-admin', 'admin', 'super-admin'].includes(userRole)

    // Run searches in parallel for better performance
    const searchPromises: Promise<SearchResult[]>[] = []

    if (hasEmployeeAccess) {
      searchPromises.push(searchEmployees(sanitizedQuery, 6)) // More employees as they're primary
    }

    if (hasAdminAccess) {
      searchPromises.push(searchDepartments(sanitizedQuery, 3))
      searchPromises.push(searchManagers(sanitizedQuery, 4))
      searchPromises.push(searchPeriods(sanitizedQuery, 2))
    }

    // Always search navigation and PTO (role filtering happens in the functions)
    searchPromises.push(searchNavigation(sanitizedQuery))
    searchPromises.push(searchPTO(sanitizedQuery, 2))

    console.log('🔍 [FTS DEBUG] Running', searchPromises.length, 'parallel searches')

    // Execute all searches in parallel
    const searchResults = await Promise.all(searchPromises)

    // Flatten and combine all results
    const allResults = searchResults.flat()

    console.log('🔍 [FTS DEBUG] Raw results before processing:', {
      totalResults: allResults.length,
      byType: allResults.reduce((acc, r) => {
        acc[r.type] = (acc[r.type] || 0) + 1
        return acc
      }, {} as Record<string, number>)
    })

    // Deduplicate results (important for dual-role entities)
    const deduplicatedResults = deduplicateResults(allResults)

    // Sort by adjusted score (highest first)
    const sortedResults = deduplicatedResults.sort((a, b) => b.adjustedScore - a.adjustedScore)

    // Limit total results to prevent overwhelming UI
    const finalResults = sortedResults.slice(0, 15)

    console.log('🔍 [FTS DEBUG] Final results:', {
      totalResults: finalResults.length,
      results: finalResults.map(r => ({
        type: r.type,
        title: r.title,
        score: r.adjustedScore
      }))
    })

    return finalResults

  } catch (error) {
    console.error('❌ [FTS ERROR] Search orchestration failed:', error)
    return []
  }
}

/**
 * Initialize Full-Text Search indexes for optimal performance
 * This should be run once during setup to create the necessary indexes
 */
export async function initializeFTSIndexes(): Promise<void> {
  console.log('🚀 [FTS SETUP] Initializing Full-Text Search indexes...')

  const indexes = [
    // Employee full-text search index
    `CREATE INDEX IF NOT EXISTS idx_employees_fts
     ON appy_employees USING GIN (to_tsvector('english', full_name))`,

    // Department full-text search index
    `CREATE INDEX IF NOT EXISTS idx_departments_fts
     ON appy_departments USING GIN (to_tsvector('english', name))`,

    // Manager full-text search index
    `CREATE INDEX IF NOT EXISTS idx_managers_fts
     ON appy_managers USING GIN (to_tsvector('english', full_name))`,

    // Appraisal period full-text search index
    `CREATE INDEX IF NOT EXISTS idx_periods_fts
     ON appy_appraisal_periods USING GIN (to_tsvector('english', name))`,

    // PTO request type full-text search index
    `CREATE INDEX IF NOT EXISTS idx_pto_fts
     ON appy_pto_requests USING GIN (to_tsvector('english', request_type))`
  ]

  try {
    for (const indexSQL of indexes) {
      await supabaseAdmin.rpc('exec_sql', { sql: indexSQL })
      console.log('✅ [FTS SETUP] Index created successfully')
    }

    console.log('🚀 [FTS SETUP] All FTS indexes initialized successfully!')
  } catch (error) {
    console.error('❌ [FTS SETUP] Failed to initialize indexes:', error)
    throw error
  }
}
```

### 8. `components/app-header.tsx` (Search Integration)

```tsx
import React from "react"
import { usePathname } from "next/navigation"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import { Search } from "lucide-react"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { useGlobalSearch } from "@/hooks/use-global-search"

// A helper to capitalize strings
const capitalize = (s: string) => s.charAt(0).toUpperCase() + s.slice(1)

export function AppHeader() {
  const pathname = usePathname()
  const segments = pathname.split("/").filter(Boolean)
  const { setIsOpen } = useGlobalSearch()

  return (
    <header className="sticky top-0 z-10 flex h-16 shrink-0 items-center gap-4 border-b bg-background px-4 sm:px-6">
      <SidebarTrigger />
      <Separator orientation="vertical" className="h-6" />

      {/* Breadcrumb Navigation */}
      <Breadcrumb className="flex-1">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
          </BreadcrumbItem>
          {segments.slice(1).map((segment, index) => {
            const href = "/" + segments.slice(0, index + 2).join("/")
            const isLast = index === segments.length - 2

            return (
              <React.Fragment key={segment}>
                <BreadcrumbSeparator />
                <BreadcrumbItem>
                  {isLast ? (
                    <BreadcrumbPage>{capitalize(segment)}</BreadcrumbPage>
                  ) : (
                    <BreadcrumbLink href={href}>
                      {capitalize(segment)}
                    </BreadcrumbLink>
                  )}
                </BreadcrumbItem>
              </React.Fragment>
            )
          })}
        </BreadcrumbList>
      </Breadcrumb>

      {/* Search Button */}
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsOpen(true)}
          className="h-8 w-8 p-0 sm:w-auto sm:px-3 sm:gap-2"
        >
          <Search className="h-4 w-4" />
          <span className="hidden sm:inline">Search</span>
          <kbd className="hidden sm:inline pointer-events-none h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 ml-2">
            <span className="text-xs">⌘</span>K
          </kbd>
        </Button>
      </div>
    </header>
  )
}
```

### 9. `app/dashboard/layout.tsx` (Search Integration)

```tsx
import type React from "react"
import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import { AppSidebar } from "@/components/app-sidebar"
import { SidebarProvider } from "@/components/ui/sidebar"
import { AppHeader } from "@/components/app-header"
import { getCurrentUser } from "@/lib/auth"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { AlertTriangle } from "lucide-react"
import { DevPerformanceMonitor } from "@/components/performance-monitor"
import { GlobalSearch } from "@/components/global-search"

// Mock user data for development
const mockUser = {
  id: "user_2zgIPJfN1J3mcwjHH27wFQT9NQW",
  name: "Francesco Oddo",
  email: "<EMAIL>",
  role: "super-admin" as const,
}

function ErrorAlert() {
  return (
    <Alert variant="destructive" className="mx-4 mt-4">
      <AlertTriangle className="h-4 w-4" />
      <AlertDescription>
        Authentication system is in development mode. Using mock user data.
      </AlertDescription>
    </Alert>
  )
}

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const cookieStore = await cookies()
  const defaultOpen = cookieStore.get("sidebar:state")?.value === "true"

  return (
    <SidebarProvider defaultOpen={defaultOpen}>
      {/* Skip Links */}
      <div className="sr-only focus-within:not-sr-only">
        <a
          href="#main-content"
          className="absolute top-4 left-4 z-50 bg-primary text-primary-foreground px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-ring"
        >
          Skip to main content
        </a>
        <a
          href="#navigation"
          className="absolute top-4 left-32 z-50 bg-primary text-primary-foreground px-4 py-2 rounded focus:outline-none focus:ring-2 focus:ring-ring"
        >
          Skip to navigation
        </a>
      </div>

      <AppSidebar user={mockUser} />
      <main id="main-content" className="flex-1 bg-muted/40" role="main">
        <AppHeader />
        <ErrorAlert />
        <div className="container mx-auto p-4">
          {children}
        </div>
      </main>

      {/* Global Search Component */}
      <GlobalSearch />

      {/* Development Performance Monitor */}
      <DevPerformanceMonitor />
    </SidebarProvider>
  )
}
```

### 10. `scripts/setup-fts-indexes.ts`

```tsx
#!/usr/bin/env tsx

/**
 * Setup script to initialize Full-Text Search indexes for the Employee Appraisal System
 *
 * This script creates GIN indexes for FTS on all searchable text fields.
 * Run this once to enable high-performance search capabilities.
 */

import { initializeFTSIndexes } from '../lib/search-fts'

async function main() {
  console.log('🚀 Setting up Full-Text Search indexes...')

  try {
    await initializeFTSIndexes()
    console.log('✅ FTS indexes setup completed successfully!')
    console.log('')
    console.log('🔍 Your search system is now ready to use Postgres FTS for:')
    console.log('  • Employee names with ts_rank scoring')
    console.log('  • Department names with relevance ranking')
    console.log('  • Manager names with fuzzy matching')
    console.log('  • Appraisal period names')
    console.log('  • PTO request types')
    console.log('')
    console.log('💡 The search will automatically fall back to ILIKE queries if FTS fails.')

    process.exit(0)
  } catch (error) {
    console.error('❌ Failed to setup FTS indexes:', error)
    process.exit(1)
  }
}

if (require.main === module) {
  main()
}
```

### 11. `lib/actions/index.ts` (Search Export)

```tsx
// Re-export all actions from their respective files
// This allows for backward compatibility while organizing actions by domain
// Note: Each individual action module contains "use server" directive

// Export shared utilities
export {
  AuthenticationError,
  AuthorizationError,
  ValidationError,
  RateLimitError,
  handleServerActionError
} from './shared'

// Export department actions
export {
  saveDepartmentAction
} from './departments'

// Export manager actions
export {
  saveManagerAction,
  createManagerAndRedirectAction
} from './managers'

// Export employee actions
export {
  saveEmployeeAction,
  deleteEmployeeAction,
  assignManagersToEmployeeAction
} from './employees'

// Export period actions
export {
  savePeriodAction
} from './periods'

// Export appraisal actions
export {
  saveAppraisalDraftAction,
  submitAppraisalAction,
  createAppraisalRevisionAction,
  resubmitAppraisalRevisionAction,
  createRevisionFromSubmittedAction
} from './appraisals'

// Export search actions
export {
  searchAllEntities
} from './search'

// Export approval actions
export {
  approveAppraisalAction,
  rejectAppraisalAction
} from './approvals'

// Export bulk actions
export {
  bulkAppraisalAction
} from './bulk'

// Export export actions
export {
  exportAppraisalsAction
} from './export'

// Export PTO actions
export {
  submitPTORequestAction,
  approvePTORequestAction,
  cancelPTORequestAction,
  initializePTOBalancesAction
} from './pto'
```

### 12. `components/ui/command.tsx` (Command Palette Components)

```tsx
"use client"

import * as React from "react"
import { type DialogProps } from "@radix-ui/react-dialog"
import { Command as CommandPrimitive } from "cmdk"
import { Search } from "lucide-react"

import { cn } from "@/lib/utils"
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog"

const Command = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive>,
  React.ComponentPropsWithoutRef<typeof CommandPrimitive>
>(({ className, ...props }, ref) => (
  <CommandPrimitive
    ref={ref}
    className={cn(
      "flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",
      className
    )}
    {...props}
  />
))
Command.displayName = CommandPrimitive.displayName

const CommandDialog = ({ children, ...props }: DialogProps) => {
  return (
    <Dialog {...props}>
      <DialogContent className="overflow-hidden p-0 shadow-lg">
        <DialogTitle className="sr-only">Search</DialogTitle>
        <Command className="[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5">
          {children}
        </Command>
      </DialogContent>
    </Dialog>
  )
}

const CommandInput = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive.Input>,
  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Input>
>(({ className, ...props }, ref) => (
  <div className="flex items-center border-b px-3" cmdk-input-wrapper="">
    <Search className="mr-2 h-4 w-4 shrink-0 opacity-50" />
    <CommandPrimitive.Input
      ref={ref}
      className={cn(
        "flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
      {...props}
    />
  </div>
))
CommandInput.displayName = CommandPrimitive.Input.displayName

const CommandList = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive.List>,
  React.ComponentPropsWithoutRef<typeof CommandPrimitive.List>
>(({ className, ...props }, ref) => (
  <CommandPrimitive.List
    ref={ref}
    className={cn("max-h-[300px] overflow-y-auto overflow-x-hidden", className)}
    {...props}
  />
))
CommandList.displayName = CommandPrimitive.List.displayName

const CommandEmpty = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive.Empty>,
  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Empty>
>(({ className, ...props }, ref) => (
  <CommandPrimitive.Empty
    ref={ref}
    className={cn("py-6 text-center text-sm", className)}
    {...props}
  />
))
CommandEmpty.displayName = CommandPrimitive.Empty.displayName

const CommandGroup = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive.Group>,
  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Group>
>(({ className, ...props }, ref) => (
  <CommandPrimitive.Group
    ref={ref}
    className={cn(
      "overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground",
      className
    )}
    {...props}
  />
))
CommandGroup.displayName = CommandPrimitive.Group.displayName

const CommandSeparator = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive.Separator>,
  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Separator>
>(({ className, ...props }, ref) => (
  <CommandPrimitive.Separator
    ref={ref}
    className={cn("-mx-1 h-px bg-border", className)}
    {...props}
  />
))
CommandSeparator.displayName = CommandPrimitive.Separator.displayName

const CommandItem = React.forwardRef<
  React.ElementRef<typeof CommandPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Item>
>(({ className, ...props }, ref) => (
  <CommandPrimitive.Item
    ref={ref}
    className={cn(
      "relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none aria-selected:bg-accent aria-selected:text-accent-foreground data-[disabled=true]:pointer-events-none data-[disabled=true]:opacity-50",
      className
    )}
    {...props}
  />
))
CommandItem.displayName = CommandPrimitive.Item.displayName

const CommandShortcut = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLSpanElement>) => {
  return (
    <span
      className={cn(
        "ml-auto text-xs tracking-widest text-muted-foreground",
        className
      )}
      {...props}
    />
  )
}
CommandShortcut.displayName = "CommandShortcut"

export {
  Command,
  CommandDialog,
  CommandInput,
  CommandList,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandShortcut,
  CommandSeparator,
}
```

### 13. Supporting Files Summary

**`lib/debug.ts`** - Debug utilities for conditional logging
**`lib/auth.ts`** - Authentication functions (validateSession, getCurrentUser, checkRateLimit, logUserAction)
**`lib/supabase.ts`** - Supabase client configuration
**`components/ui/badge.tsx`** - Badge component for result type labels
**`package.json`** - Dependencies (lodash, cmdk, @radix-ui/react-dialog, lucide-react)

---

## 🎯 Search System Architecture Summary

### **Data Flow**
1. **User Input** → `GlobalSearch` component
2. **State Management** → `useGlobalSearch` hook
3. **Debounced API Call** → `searchAllEntities` server action
4. **Authentication & Rate Limiting** → Auth middleware
5. **Full-Text Search** → `searchAllEntitiesFTS` function
6. **Parallel Entity Searches** → Individual search functions
7. **Result Processing** → Deduplication, scoring, sorting
8. **UI Rendering** → Grouped results with icons and badges

### **Key Features**
- ✅ **Command Palette Interface** with keyboard navigation
- ✅ **Keyboard Shortcuts** (Cmd/Ctrl+K, Escape)
- ✅ **Debounced Search** (300ms delay)
- ✅ **Recent Searches** with localStorage persistence
- ✅ **Postgres Full-Text Search** with ILIKE fallback
- ✅ **Role-based Access Control**
- ✅ **Multi-entity Search** (6 entity types)
- ✅ **Result Grouping** and visual organization
- ✅ **Comprehensive Logging** for debugging
- ✅ **Performance Optimization** with parallel searches
- ✅ **Error Handling** and graceful degradation

### **Security & Performance**
- 🔐 **Authentication** validation on all requests
- 🔐 **Rate Limiting** (30 requests per minute)
- 🔐 **Input Sanitization** for search queries
- ⚡ **FTS Indexes** for fast text search
- ⚡ **Parallel Execution** of entity searches
- ⚡ **Result Caching** in component state
- ⚡ **Debounced API Calls** to reduce server load

This comprehensive search system provides a modern, performant, and secure global search experience across the entire Employee Appraisal Management System.
