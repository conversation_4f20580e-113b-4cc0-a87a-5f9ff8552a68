# Global Search Testing Plan

## Overview
This document outlines the testing plan for the global search access control fixes to ensure proper data scoping and security.

## Test Scenarios

### 1. Employee Search Access Control

#### Test 1.1: Super Admin Access
- **User Role**: Super Admin
- **Expected**: Can search and see all employees
- **Test Steps**:
  1. <PERSON><PERSON> as super admin
  2. Search for "mona" 
  3. Verify all employees named <PERSON> appear
  4. Check subtitle shows department and rate info

#### Test 1.2: Manager Access
- **User Role**: Manager
- **Expected**: Only sees direct reports
- **Test Steps**:
  1. <PERSON><PERSON> as manager
  2. Search for employee names
  3. Verify only direct reports appear
  4. Check subtitle hides compensation info

#### Test 1.3: Employee Access
- **User Role**: Regular Employee
- **Expected**: Only sees themselves
- **Test Steps**:
  1. <PERSON><PERSON> as regular employee
  2. Search for their own name
  3. Verify only their record appears
  4. Search for other employee names
  5. Verify no results returned

### 2. PTO Search Access Control

#### Test 2.1: Admin PTO Access
- **User Role**: Super Admin/HR Admin
- **Expected**: Can see all PTO requests
- **Test Steps**:
  1. <PERSON><PERSON> as admin
  2. Search for "vacation" or "sick"
  3. Verify PTO requests from all employees appear

#### Test 2.2: Manager PTO Access
- **User Role**: Manager
- **Expected**: Only sees team PTO requests
- **Test Steps**:
  1. Login as manager
  2. Search for PTO types
  3. Verify only direct reports' PTO appears

#### Test 2.3: Employee PTO Access
- **User Role**: Regular Employee
- **Expected**: Only sees own PTO requests
- **Test Steps**:
  1. Login as employee
  2. Search for PTO types
  3. Verify only their own PTO requests appear

### 3. Manager Search Access Control

#### Test 3.1: Admin Manager Search
- **User Role**: Admin
- **Expected**: Can search all managers
- **Test Steps**:
  1. Login as admin
  2. Search for manager names
  3. Verify all managers appear

#### Test 3.2: Manager-to-Manager Search
- **User Role**: Manager
- **Expected**: Can see other managers
- **Test Steps**:
  1. Login as manager
  2. Search for other manager names
  3. Verify other managers appear

#### Test 3.3: Employee Manager Search
- **User Role**: Regular Employee
- **Expected**: Cannot search managers
- **Test Steps**:
  1. Login as employee
  2. Search for manager names
  3. Verify no manager results appear

### 4. Search Term Length Testing

#### Test 4.1: Single Character Search
- **Test Steps**:
  1. Enter single character (e.g., "m")
  2. Verify search executes
  3. Check results are properly filtered

#### Test 4.2: Two Character Search
- **Test Steps**:
  1. Enter two characters (e.g., "mo")
  2. Verify search executes immediately
  3. Check results are relevant

#### Test 4.3: Special Character Handling
- **Test Steps**:
  1. Enter search with special chars (e.g., "mona@")
  2. Verify search sanitizes properly
  3. Check results still appear

### 5. Data Privacy Testing

#### Test 5.1: Compensation Data Hiding
- **User Role**: Manager (non-admin)
- **Expected**: Cannot see compensation info
- **Test Steps**:
  1. Login as manager
  2. Search for employees
  3. Verify subtitle shows "Department • Employee" not rate info

#### Test 5.2: Sensitive Data Exposure
- **User Role**: Regular Employee
- **Expected**: Cannot see other employees' data
- **Test Steps**:
  1. Login as employee
  2. Search for various terms
  3. Verify no unauthorized data appears

### 6. Error Handling Testing

#### Test 6.1: Database Connection Issues
- **Test Steps**:
  1. Simulate database connectivity issues
  2. Verify graceful error handling
  3. Check user sees appropriate message

#### Test 6.2: Invalid Search Terms
- **Test Steps**:
  1. Enter very long search terms
  2. Enter only special characters
  3. Verify system handles gracefully

### 7. Performance Testing

#### Test 7.1: Search Response Time
- **Test Steps**:
  1. Measure search response times
  2. Verify under 2 seconds for typical queries
  3. Check with various user roles

#### Test 7.2: Concurrent Search Load
- **Test Steps**:
  1. Simulate multiple users searching
  2. Verify system remains responsive
  3. Check access control still works

## Expected Results Summary

| User Role | Employees | Managers | PTO | Departments | Periods |
|-----------|-----------|----------|-----|-------------|---------|
| Super Admin | All | All | All | All | All |
| HR Admin | All | All | All | All | All |
| Admin | All | All | All | All | All |
| Manager | Direct reports only | All | Team only | None | None |
| Employee | Self only | None | Own only | None | None |

## Logging Verification

### Check Console Logs For:
1. Access control decisions
2. User context information
3. Query filtering details
4. Search result counts by type
5. Error handling messages

### Sample Log Patterns:
```
🔍 [FTS DEBUG] === SEARCH COMPLETE WITH ACCESS CONTROL ===
👔 [ACCESS DEBUG] Manager access: filtering to managed employees
🚫 [ACCESS DEBUG] No employee access for user
✅ [FTS DEBUG] Found employees with access control
```

## Security Validation

### Verify No Data Leakage:
1. Check network requests don't expose unauthorized data
2. Verify database queries include proper filters
3. Confirm error messages don't reveal sensitive info
4. Test edge cases with dual-role users (manager + employee)

## Browser Testing

### Test Across:
- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Mobile browsers

### Verify:
- Search functionality works consistently
- Access control applies correctly
- Performance is acceptable
- UI displays properly
